import React, { useState } from 'react';
import styled from 'styled-components';
import { AISolution } from '../../../types';

const Container = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
  }
`;

const TabContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
`;

const Tab = styled.button<{ active: boolean }>`
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.active ? `
    background: rgba(255, 255, 255, 0.2);
    color: white;
  ` : `
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.7);
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  `}
`;

const Content = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  opacity: 0.7;
  
  .icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
  }
`;

const GenerateButton = styled.button<{ disabled?: boolean }>`
  width: 100%;
  padding: 40px 20px;
  border: 2px dashed rgba(102, 126, 234, 0.5);
  border-radius: 12px;
  background: transparent;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.disabled ? `
    opacity: 0.5;
    cursor: not-allowed;
    border-color: rgba(255, 255, 255, 0.2);
  ` : `
    &:hover {
      border-color: rgba(102, 126, 234, 0.8);
      background: rgba(102, 126, 234, 0.1);
    }
  `}
  
  .icon {
    font-size: 32px;
    margin-bottom: 8px;
    display: block;
  }
`;

const SolutionDetails = styled.div`
  .meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 12px;
    opacity: 0.8;
    
    .language {
      background: rgba(102, 126, 234, 0.2);
      color: #667eea;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: 500;
    }
  }
`;

const CodeBlock = styled.div`
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  position: relative;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 12px;
    opacity: 0.8;
  }
  
  .copy-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
  
  pre {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
    font-size: 13px;
    line-height: 1.5;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
`;

const Section = styled.div`
  margin-bottom: 20px;
  
  .title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .content {
    font-size: 14px;
    line-height: 1.6;
    opacity: 0.9;
  }
`;

const ComplexityGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  
  .complexity-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 8px;
    
    .label {
      font-size: 12px;
      opacity: 0.7;
      margin-bottom: 4px;
    }
    
    .value {
      font-size: 14px;
      font-weight: 600;
      font-family: monospace;
    }
  }
`;

interface SolutionDisplayProps {
  solution: AISolution | null;
  onGenerateSolution: () => void;
  isLoading: boolean;
  hasApiKey: boolean;
  hasProblem: boolean;
}

const SolutionDisplay: React.FC<SolutionDisplayProps> = ({
  solution,
  onGenerateSolution,
  isLoading,
  hasApiKey,
  hasProblem
}) => {
  const [activeTab, setActiveTab] = useState<'solution' | 'explanation'>('solution');

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getEmptyStateMessage = () => {
    if (!hasApiKey) {
      return {
        icon: '🔑',
        title: 'API Key Required',
        message: 'Configure your Gemini API key in settings to generate solutions'
      };
    }
    if (!hasProblem) {
      return {
        icon: '📝',
        title: 'No Problem Loaded',
        message: 'Capture a screenshot of a coding problem first'
      };
    }
    return {
      icon: '🤖',
      title: 'Ready to Generate',
      message: 'Click below or press ⌘+↵ to generate an AI solution'
    };
  };

  const emptyState = getEmptyStateMessage();

  return (
    <Container>
      <Header>
        <h2>AI Solution</h2>
      </Header>
      
      {solution && (
        <TabContainer>
          <Tab 
            active={activeTab === 'solution'} 
            onClick={() => setActiveTab('solution')}
          >
            Code Solution
          </Tab>
          <Tab 
            active={activeTab === 'explanation'} 
            onClick={() => setActiveTab('explanation')}
          >
            Explanation
          </Tab>
        </TabContainer>
      )}
      
      <Content>
        {solution ? (
          <SolutionDetails>
            <div className="meta">
              <span className="language">{solution.language.toUpperCase()}</span>
              <span>{formatTimestamp(solution.timestamp)}</span>
            </div>
            
            {activeTab === 'solution' ? (
              <>
                <CodeBlock>
                  <div className="header">
                    <span>Solution Code</span>
                    <button 
                      className="copy-btn"
                      onClick={() => copyToClipboard(solution.code)}
                    >
                      📋 Copy
                    </button>
                  </div>
                  <pre>{solution.code}</pre>
                </CodeBlock>
                
                <ComplexityGrid>
                  <div className="complexity-item">
                    <div className="label">Time Complexity</div>
                    <div className="value">{solution.timeComplexity}</div>
                  </div>
                  <div className="complexity-item">
                    <div className="label">Space Complexity</div>
                    <div className="value">{solution.spaceComplexity}</div>
                  </div>
                </ComplexityGrid>
              </>
            ) : (
              <Section>
                <div className="title">
                  💡 Explanation
                </div>
                <div className="content">{solution.explanation}</div>
              </Section>
            )}
          </SolutionDetails>
        ) : (
          <EmptyState>
            {hasProblem && hasApiKey && !isLoading ? (
              <GenerateButton 
                onClick={onGenerateSolution}
                disabled={isLoading || !hasProblem || !hasApiKey}
              >
                <span className="icon">🤖</span>
                Generate AI Solution
                <br />
                <small style={{ opacity: 0.7 }}>Press ⌘+↵ or click here</small>
              </GenerateButton>
            ) : (
              <>
                <div className="icon">{emptyState.icon}</div>
                <h3>{emptyState.title}</h3>
                <p>{emptyState.message}</p>
              </>
            )}
          </EmptyState>
        )}
      </Content>
    </Container>
  );
};

export default SolutionDisplay;
