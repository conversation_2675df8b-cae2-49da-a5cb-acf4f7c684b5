import React, { useState } from 'react';
import styled from 'styled-components';
import { CodingProblem } from '../../../types';

const Container = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
  }
`;

const TabContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
`;

const Tab = styled.button<{ active: boolean }>`
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.active ? `
    background: rgba(255, 255, 255, 0.2);
    color: white;
  ` : `
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.7);
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  `}
`;

const Content = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  opacity: 0.7;
  
  .icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
  }
`;

const ProblemCard = styled.div<{ isActive?: boolean }>`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid ${props => props.isActive ? 'rgba(102, 126, 234, 0.5)' : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
  }
  
  .title {
    font-weight: 600;
    margin-bottom: 8px;
    color: ${props => props.isActive ? '#667eea' : 'white'};
  }
  
  .description {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    opacity: 0.6;
  }
  
  .difficulty {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    
    &.Easy {
      background: rgba(34, 197, 94, 0.2);
      color: #22c55e;
    }
    
    &.Medium {
      background: rgba(251, 191, 36, 0.2);
      color: #fbbf24;
    }
    
    &.Hard {
      background: rgba(239, 68, 68, 0.2);
      color: #ef4444;
    }
  }
`;

const ProblemDetails = styled.div`
  .title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
  }
  
  .description {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 16px;
    white-space: pre-wrap;
  }
  
  .meta {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    font-size: 12px;
    
    .item {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
  
  .screenshot {
    margin-top: 16px;
    
    img {
      max-width: 100%;
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  }
`;

const CaptureButton = styled.button`
  width: 100%;
  padding: 40px 20px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: transparent;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.05);
  }
  
  .icon {
    font-size: 32px;
    margin-bottom: 8px;
    display: block;
  }
`;

interface ProblemDisplayProps {
  problem: CodingProblem | null;
  problems: CodingProblem[];
  onLoadProblem: (problem: CodingProblem) => void;
  onCaptureScreenshot: () => void;
}

const ProblemDisplay: React.FC<ProblemDisplayProps> = ({
  problem,
  problems,
  onLoadProblem,
  onCaptureScreenshot
}) => {
  const [activeTab, setActiveTab] = useState<'current' | 'history'>('current');

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <Container>
      <Header>
        <h2>Problem</h2>
      </Header>
      
      <TabContainer>
        <Tab 
          active={activeTab === 'current'} 
          onClick={() => setActiveTab('current')}
        >
          Current Problem
        </Tab>
        <Tab 
          active={activeTab === 'history'} 
          onClick={() => setActiveTab('history')}
        >
          History ({problems.length})
        </Tab>
      </TabContainer>
      
      <Content>
        {activeTab === 'current' ? (
          problem ? (
            <ProblemDetails>
              <div className="title">{problem.title}</div>
              <div className="meta">
                <div className="item">
                  <span className={`difficulty ${problem.difficulty}`}>
                    {problem.difficulty}
                  </span>
                </div>
                <div className="item">
                  📅 {formatTimestamp(problem.timestamp)}
                </div>
              </div>
              <div className="description">{problem.description}</div>
              {problem.screenshotPath && (
                <div className="screenshot">
                  <img src={`file://${problem.screenshotPath}`} alt="Problem Screenshot" />
                </div>
              )}
            </ProblemDetails>
          ) : (
            <EmptyState>
              <CaptureButton onClick={onCaptureScreenshot}>
                <span className="icon">📸</span>
                Capture Screenshot to Get Started
                <br />
                <small style={{ opacity: 0.7 }}>Press ⌘+H or click here</small>
              </CaptureButton>
            </EmptyState>
          )
        ) : (
          problems.length > 0 ? (
            problems.map(p => (
              <ProblemCard 
                key={p.id} 
                isActive={problem?.id === p.id}
                onClick={() => onLoadProblem(p)}
              >
                <div className="title">{p.title}</div>
                <div className="description">{p.description}</div>
                <div className="meta">
                  <span className={`difficulty ${p.difficulty}`}>
                    {p.difficulty}
                  </span>
                  <span>{formatTimestamp(p.timestamp)}</span>
                </div>
              </ProblemCard>
            ))
          ) : (
            <EmptyState>
              <div className="icon">📚</div>
              <h3>No Problems Yet</h3>
              <p>Captured problems will appear here</p>
            </EmptyState>
          )
        )}
      </Content>
    </Container>
  );
};

export default ProblemDisplay;
