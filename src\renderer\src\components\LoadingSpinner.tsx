import React from 'react';
import styled, { keyframes } from 'styled-components';

const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const pulse = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
`;

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
`;

const Container = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  color: white;
  min-width: 300px;
`;

const SpinnerContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
`;

const Spinner = styled.div`
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
`;

const Message = styled.div`
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  animation: ${pulse} 2s ease-in-out infinite;
`;

const SubMessage = styled.div`
  font-size: 14px;
  opacity: 0.7;
`;

const DotsContainer = styled.div`
  display: inline-flex;
  gap: 4px;
  margin-left: 4px;
`;

const Dot = styled.span<{ delay: number }>`
  width: 4px;
  height: 4px;
  background: currentColor;
  border-radius: 50%;
  animation: ${pulse} 1.5s ease-in-out infinite;
  animation-delay: ${props => props.delay}s;
`;

interface LoadingSpinnerProps {
  message?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  message = 'Loading' 
}) => {
  const getSubMessage = (msg: string) => {
    switch (msg.toLowerCase()) {
      case 'capturing screenshot...':
        return 'Taking a screenshot of your screen';
      case 'extracting text...':
        return 'Using OCR to read the problem text';
      case 'generating ai solution...':
        return 'AI is analyzing the problem and creating a solution';
      default:
        return 'Please wait while we process your request';
    }
  };

  return (
    <Overlay>
      <Container>
        <SpinnerContainer>
          <Spinner />
        </SpinnerContainer>
        
        <Message>
          {message}
          <DotsContainer>
            <Dot delay={0} />
            <Dot delay={0.2} />
            <Dot delay={0.4} />
          </DotsContainer>
        </Message>
        
        <SubMessage>
          {getSubMessage(message)}
        </SubMessage>
      </Container>
    </Overlay>
  );
};

export default LoadingSpinner;
