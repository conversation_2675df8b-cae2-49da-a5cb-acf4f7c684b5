@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\@electron+osx-sign@1.0.5\node_modules\@electron\osx-sign\bin\node_modules;C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\@electron+osx-sign@1.0.5\node_modules\@electron\osx-sign\node_modules;C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\@electron+osx-sign@1.0.5\node_modules\@electron\node_modules;C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\@electron+osx-sign@1.0.5\node_modules;C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\@electron+osx-sign@1.0.5\node_modules\@electron\osx-sign\bin\node_modules;C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\@electron+osx-sign@1.0.5\node_modules\@electron\osx-sign\node_modules;C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\@electron+osx-sign@1.0.5\node_modules\@electron\node_modules;C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\@electron+osx-sign@1.0.5\node_modules;C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\electron-osx-sign.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\electron-osx-sign.js" %*
)
