hoistPattern:
  - '*'
hoistedDependencies:
  7zip-bin@5.2.0:
    7zip-bin: private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.2':
    '@babel/compat-data': private
  '@babel/core@7.27.1':
    '@babel/core': private
  '@babel/generator@7.27.1':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.1':
    '@babel/helpers': private
  '@babel/parser@7.27.2':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.1':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.1':
    '@babel/traverse': private
  '@babel/types@7.27.1':
    '@babel/types': private
  '@develar/schema-utils@2.6.5':
    '@develar/schema-utils': private
  '@electron/asar@3.4.1':
    '@electron/asar': private
  '@electron/get@2.0.3':
    '@electron/get': private
  '@electron/notarize@2.2.1':
    '@electron/notarize': private
  '@electron/osx-sign@1.0.5':
    '@electron/osx-sign': private
  '@electron/universal@1.5.1':
    '@electron/universal': private
  '@emotion/is-prop-valid@1.2.2':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.8.1':
    '@emotion/memoize': private
  '@emotion/unitless@0.8.1':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': public
  '@eslint/js@8.57.1':
    '@eslint/js': public
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@malept/cross-spawn-promise@1.1.1':
    '@malept/cross-spawn-promise': private
  '@malept/flatpak-bundler@0.4.0':
    '@malept/flatpak-bundler': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@rolldown/pluginutils@1.0.0-beta.9':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/fs-extra@9.0.13':
    '@types/fs-extra': private
  '@types/hoist-non-react-statics@3.3.6':
    '@types/hoist-non-react-statics': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/stylis@4.2.5':
    '@types/stylis': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@xmldom/xmldom@0.8.10':
    '@xmldom/xmldom': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  app-builder-bin@4.0.0:
    app-builder-bin: private
  app-builder-lib@24.13.3(dmg-builder@24.13.3)(electron-builder-squirrel-windows@24.13.3):
    app-builder-lib: private
  archiver-utils@2.1.0:
    archiver-utils: private
  archiver@5.3.2:
    archiver: private
  argparse@2.0.1:
    argparse: private
  array-union@2.1.0:
    array-union: private
  async-exit-hook@2.0.1:
    async-exit-hook: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  atomically@1.7.0:
    atomically: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bl@4.1.0:
    bl: private
  bluebird-lst@1.0.9:
    bluebird-lst: private
  bluebird@3.7.2:
    bluebird: private
  bmp-js@0.1.0:
    bmp-js: private
  boolean@3.2.0:
    boolean: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.5:
    browserslist: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal@1.0.1:
    buffer-equal: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  builder-util-runtime@9.2.4:
    builder-util-runtime: private
  builder-util@24.13.1:
    builder-util: private
  cacheable-lookup@5.0.4:
    cacheable-lookup: private
  cacheable-request@7.0.4:
    cacheable-request: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  camelize@1.0.1:
    camelize: private
  caniuse-lite@1.0.30001718:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chownr@2.0.0:
    chownr: private
  chromium-pickle-js@0.2.0:
    chromium-pickle-js: private
  ci-info@3.9.0:
    ci-info: private
  cliui@8.0.1:
    cliui: private
  clone-response@1.0.3:
    clone-response: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@5.1.0:
    commander: private
  compare-version@0.1.2:
    compare-version: private
  compress-commons@4.1.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  conf@10.2.0:
    conf: private
  config-file-ts@0.2.6:
    config-file-ts: private
  convert-source-map@2.0.0:
    convert-source-map: private
  core-util-is@1.0.3:
    core-util-is: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@4.0.3:
    crc32-stream: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-color-keywords@1.0.0:
    css-color-keywords: private
  css-to-react-native@3.2.0:
    css-to-react-native: private
  csstype@3.1.3:
    csstype: private
  date-fns@2.30.0:
    date-fns: private
  debounce-fn@4.0.0:
    debounce-fn: private
  debug@4.4.1:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-is@0.1.4:
    deep-is: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  detect-node@2.1.0:
    detect-node: private
  dir-compare@3.3.0:
    dir-compare: private
  dir-glob@3.0.1:
    dir-glob: private
  dmg-builder@24.13.3(electron-builder-squirrel-windows@24.13.3):
    dmg-builder: private
  dmg-license@1.0.11:
    dmg-license: private
  doctrine@3.0.0:
    doctrine: private
  dot-prop@6.0.1:
    dot-prop: private
  dotenv-expand@5.1.0:
    dotenv-expand: private
  dotenv@9.0.2:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ejs@3.1.10:
    ejs: private
  electron-builder-squirrel-windows@24.13.3(dmg-builder@24.13.3):
    electron-builder-squirrel-windows: private
  electron-is-accelerator@0.1.2:
    electron-is-accelerator: private
  electron-publish@24.13.1:
    electron-publish: private
  electron-to-chromium@1.5.157:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  end-of-stream@1.4.4:
    end-of-stream: private
  env-paths@2.2.1:
    env-paths: private
  err-code@2.0.3:
    err-code: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es6-error@4.1.1:
    es6-error: private
  esbuild@0.21.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@7.2.2:
    eslint-scope: public
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: public
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.2:
    form-data: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@5.2.0:
    get-stream: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  global-agent@3.0.0:
    global-agent: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  got@11.8.6:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  hosted-git-info@4.1.0:
    hosted-git-info: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http-proxy-agent@5.0.0:
    http-proxy-agent: private
  http2-wrapper@1.0.3:
    http2-wrapper: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  iconv-lite@0.6.3:
    iconv-lite: private
  idb-keyval@6.2.2:
    idb-keyval: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-ci@3.0.1:
    is-ci: private
  is-electron@2.2.2:
    is-electron: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-obj@2.0.0:
    is-obj: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-url@1.2.4:
    is-url: private
  isarray@1.0.0:
    isarray: private
  isbinaryfile@5.0.4:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema-typed@7.0.3:
    json-schema-typed: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  keyboardevent-from-electron-accelerator@2.0.0:
    keyboardevent-from-electron-accelerator: private
  keyboardevents-areequal@0.2.2:
    keyboardevents-areequal: private
  keyv@4.5.4:
    keyv: private
  lazy-val@1.0.5:
    lazy-val: private
  lazystream@1.0.1:
    lazystream: private
  levn@0.4.1:
    levn: private
  locate-path@6.0.0:
    locate-path: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.union@4.6.0:
    lodash.union: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  lowercase-keys@2.0.0:
    lowercase-keys: private
  lru-cache@5.1.1:
    lru-cache: private
  matcher@3.0.0:
    matcher: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@3.1.0:
    mimic-fn: private
  mimic-response@3.1.0:
    mimic-response: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@5.0.0:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-fetch@2.7.0:
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-url@6.1.0:
    normalize-url: private
  object-keys@1.1.1:
    object-keys: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  opencollective-postinstall@2.0.3:
    opencollective-postinstall: private
  optionator@0.9.4:
    optionator: private
  p-cancelable@2.1.1:
    p-cancelable: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pkg-up@3.1.0:
    pkg-up: private
  plist@3.1.0:
    plist: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.4.49:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  progress@2.0.3:
    progress: private
  promise-retry@2.0.1:
    promise-retry: private
  pump@3.0.2:
    pump: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@5.1.1:
    quick-lru: private
  react-is@16.13.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  read-config-file@6.3.2:
    read-config-file: private
  readable-stream@3.6.2:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  regenerator-runtime@0.13.11:
    regenerator-runtime: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-from@4.0.0:
    resolve-from: private
  responselike@2.0.1:
    responselike: private
  retry@0.12.0:
    retry: private
  reusify@1.1.0:
    reusify: private
  rimraf@2.6.3:
    rimraf: private
  roarr@2.15.4:
    roarr: private
  rollup@4.41.1:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.1.2:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sanitize-filename@1.6.3:
    sanitize-filename: private
  sax@1.4.1:
    sax: private
  scheduler@0.23.2:
    scheduler: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@7.7.2:
    semver: private
  serialize-error@7.0.1:
    serialize-error: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.2:
    shell-quote: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  slash@3.0.0:
    slash: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  spawn-command@0.0.2:
    spawn-command: private
  sprintf-js@1.1.3:
    sprintf-js: private
  stat-mode@1.0.0:
    stat-mode: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  stylis@4.3.2:
    stylis: private
  sumchecker@3.0.1:
    sumchecker: private
  supports-color@8.1.1:
    supports-color: private
  tar-stream@2.2.0:
    tar-stream: private
  tar@6.2.1:
    tar: private
  temp-file@3.4.0:
    temp-file: private
  temp@0.9.4:
    temp: private
  tesseract.js-core@5.1.1:
    tesseract.js-core: private
  text-table@0.2.0:
    text-table: private
  tmp-promise@3.0.3:
    tmp-promise: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  truncate-utf8-bytes@1.0.2:
    truncate-utf8-bytes: private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  tslib@2.6.2:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@2.19.0:
    type-fest: private
  undici-types@6.19.8:
    undici-types: private
  universalify@2.0.1:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  utf8-byte-length@1.0.5:
    utf8-byte-length: private
  util-deprecate@1.0.2:
    util-deprecate: private
  wasm-feature-detect@1.8.0:
    wasm-feature-detect: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  xmlbuilder@15.1.1:
    xmlbuilder: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zip-stream@4.1.1:
    zip-stream: private
  zlibjs@0.3.1:
    zlibjs: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.14.2
pendingBuilds: []
prunedAt: Sat, 24 May 2025 07:25:11 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@types/plist@3.0.5'
  - '@types/verror@1.10.11'
  - assert-plus@1.0.0
  - astral-regex@2.0.0
  - cli-truncate@2.1.0
  - core-util-is@1.0.2
  - crc@3.8.0
  - dmg-license@1.0.11
  - extsprintf@1.4.1
  - fsevents@2.3.3
  - iconv-corefoundation@1.1.7
  - node-addon-api@1.7.2
  - slice-ansi@3.0.0
  - smart-buffer@4.2.0
  - verror@1.10.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\devs\web-devs\meet-xpo-v1\node_modules\.pnpm
virtualStoreDirMaxLength: 120
