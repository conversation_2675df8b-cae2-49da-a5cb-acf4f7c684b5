export interface CodingProblem {
    id: string;
    title: string;
    description: string;
    difficulty: 'Easy' | 'Medium' | 'Hard';
    tags: string[];
    screenshotPath?: string;
    timestamp: number;
}
export interface AISolution {
    id: string;
    problemId: string;
    code: string;
    language: string;
    explanation: string;
    timeComplexity: string;
    spaceComplexity: string;
    timestamp: number;
}
export interface AppSettings {
    geminiApiKey: string;
    shortcuts: {
        captureScreenshot: string;
        toggleWindow: string;
        generateSolution: string;
        resetProblem: string;
        quitApp: string;
    };
    windowSettings: {
        alwaysOnTop: boolean;
        startMinimized: boolean;
        width: number;
        height: number;
    };
    ocrSettings: {
        language: string;
        confidence: number;
    };
}
export interface IPCEvents {
    'capture-screenshot': () => Promise<string>;
    'screenshot-captured': (imagePath: string) => void;
    'extract-text': (imagePath: string) => Promise<string>;
    'text-extracted': (text: string) => void;
    'generate-solution': (problemText: string) => Promise<AISolution>;
    'solution-generated': (solution: AISolution) => void;
    'get-settings': () => Promise<AppSettings>;
    'save-settings': (settings: Partial<AppSettings>) => Promise<void>;
    'toggle-window': () => void;
    'minimize-window': () => void;
    'close-window': () => void;
    'quit-app': () => void;
    'save-problem': (problem: CodingProblem) => Promise<void>;
    'get-problems': () => Promise<CodingProblem[]>;
    'save-solution': (solution: AISolution) => Promise<void>;
    'get-solutions': (problemId: string) => Promise<AISolution[]>;
}
export interface ElectronAPI {
    captureScreenshot: () => Promise<string>;
    extractText: (imagePath: string) => Promise<string>;
    generateSolution: (problemText: string) => Promise<AISolution>;
    getSettings: () => Promise<AppSettings>;
    saveSettings: (settings: Partial<AppSettings>) => Promise<void>;
    toggleWindow: () => void;
    minimizeWindow: () => void;
    closeWindow: () => void;
    quitApp: () => void;
    saveProblem: (problem: CodingProblem) => Promise<void>;
    getProblems: () => Promise<CodingProblem[]>;
    saveSolution: (solution: AISolution) => Promise<void>;
    getSolutions: (problemId: string) => Promise<AISolution[]>;
    onScreenshotCaptured: (callback: (imagePath: string) => void) => void;
    onTextExtracted: (callback: (text: string) => void) => void;
    onSolutionGenerated: (callback: (solution: AISolution) => void) => void;
}
declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}
//# sourceMappingURL=index.d.ts.map