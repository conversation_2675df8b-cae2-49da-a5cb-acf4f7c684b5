import React from 'react';
import styled from 'styled-components';

const HeaderContainer = styled.header`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  
  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(45deg, #fff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  align-items: center;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'danger'; disabled?: boolean }>`
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  
  ${props => {
    if (props.disabled) {
      return `
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.5);
        cursor: not-allowed;
      `;
    }
    
    switch (props.variant) {
      case 'primary':
        return `
          background: linear-gradient(45deg, #667eea, #764ba2);
          color: white;
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
          }
        `;
      case 'danger':
        return `
          background: linear-gradient(45deg, #ef4444, #dc2626);
          color: white;
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
          }
        `;
      default:
        return `
          background: rgba(255, 255, 255, 0.1);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.2);
          &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
          }
        `;
    }
  }}
  
  &:active {
    transform: translateY(0);
  }
`;

const StatusIndicator = styled.div<{ status: 'ready' | 'warning' | 'error' }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  
  ${props => {
    switch (props.status) {
      case 'ready':
        return 'background: #10b981;';
      case 'warning':
        return 'background: #f59e0b;';
      case 'error':
        return 'background: #ef4444;';
    }
  }}
`;

interface HeaderProps {
  onCaptureScreenshot: () => void;
  onGenerateSolution: () => void;
  onResetProblem: () => void;
  onOpenSettings: () => void;
  onQuitApp: () => void;
  isLoading: boolean;
  hasApiKey: boolean;
  hasProblem: boolean;
}

const Header: React.FC<HeaderProps> = ({
  onCaptureScreenshot,
  onGenerateSolution,
  onResetProblem,
  onOpenSettings,
  onQuitApp,
  isLoading,
  hasApiKey,
  hasProblem
}) => {
  const getStatus = () => {
    if (!hasApiKey) return 'error';
    if (!hasProblem) return 'warning';
    return 'ready';
  };

  const getStatusText = () => {
    if (!hasApiKey) return 'API Key Required';
    if (!hasProblem) return 'No Problem Loaded';
    return 'Ready';
  };

  return (
    <HeaderContainer>
      <Logo>
        <div className="icon">CX</div>
        <h1>Code Xpo</h1>
      </Logo>
      
      <ButtonGroup>
        <div style={{ display: 'flex', alignItems: 'center', marginRight: '10px' }}>
          <StatusIndicator status={getStatus()} />
          <span style={{ fontSize: '12px', opacity: 0.8 }}>{getStatusText()}</span>
        </div>
        
        <Button 
          onClick={onCaptureScreenshot} 
          disabled={isLoading}
          title="Capture Screenshot (⌘+H)"
        >
          📸 Capture
        </Button>
        
        <Button 
          variant="primary"
          onClick={onGenerateSolution} 
          disabled={isLoading || !hasProblem || !hasApiKey}
          title="Generate Solution (⌘+↵)"
        >
          🤖 Generate
        </Button>
        
        <Button 
          onClick={onResetProblem} 
          disabled={isLoading || !hasProblem}
          title="Reset Problem (⌘+R)"
        >
          🔄 Reset
        </Button>
        
        <Button 
          onClick={onOpenSettings}
          title="Settings"
        >
          ⚙️ Settings
        </Button>
        
        <Button 
          variant="danger"
          onClick={onQuitApp}
          title="Quit App (⌘+Q)"
        >
          ❌ Quit
        </Button>
      </ButtonGroup>
    </HeaderContainer>
  );
};

export default Header;
