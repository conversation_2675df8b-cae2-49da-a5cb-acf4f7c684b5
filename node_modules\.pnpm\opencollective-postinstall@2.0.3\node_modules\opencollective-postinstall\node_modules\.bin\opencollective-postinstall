#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/devs/web-devs/meet-xpo-v1/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/node_modules:/mnt/c/Users/<USER>/devs/web-devs/meet-xpo-v1/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules:/mnt/c/Users/<USER>/devs/web-devs/meet-xpo-v1/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/devs/web-devs/meet-xpo-v1/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/node_modules:/mnt/c/Users/<USER>/devs/web-devs/meet-xpo-v1/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules:/mnt/c/Users/<USER>/devs/web-devs/meet-xpo-v1/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../index.js" "$@"
else
  exec node  "$basedir/../../index.js" "$@"
fi
