import React, { useState } from 'react';
import styled from 'styled-components';
import { AppSettings } from '../../../types';

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const Modal = styled.div`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 30px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  color: white;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  
  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const Section = styled.div`
  margin-bottom: 30px;
  
  .title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
  
  label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    opacity: 0.9;
  }
  
  .description {
    font-size: 12px;
    opacity: 0.7;
    margin-bottom: 8px;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 14px;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 14px;
  
  option {
    background: #1a1a1a;
    color: white;
  }
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  display: flex !important;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
`;

const ShortcutGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 30px;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.variant === 'primary' ? `
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  ` : `
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  `}
`;

interface SettingsModalProps {
  settings: AppSettings;
  onSave: (settings: Partial<AppSettings>) => void;
  onClose: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  settings,
  onSave,
  onClose
}) => {
  const [formData, setFormData] = useState<AppSettings>(settings);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  const updateField = (field: keyof AppSettings, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateNestedField = (parent: keyof AppSettings, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }));
  };

  return (
    <Overlay onClick={onClose}>
      <Modal onClick={(e) => e.stopPropagation()}>
        <Header>
          <h2>⚙️ Settings</h2>
          <CloseButton onClick={onClose}>×</CloseButton>
        </Header>
        
        <form onSubmit={handleSubmit}>
          <Section>
            <div className="title">🔑 API Configuration</div>
            <FormGroup>
              <label>Gemini API Key</label>
              <div className="description">
                Get your API key from Google AI Studio
              </div>
              <Input
                type="password"
                placeholder="Enter your Gemini API key"
                value={formData.geminiApiKey}
                onChange={(e) => updateField('geminiApiKey', e.target.value)}
              />
            </FormGroup>
          </Section>

          <Section>
            <div className="title">⌨️ Keyboard Shortcuts</div>
            <ShortcutGrid>
              <FormGroup>
                <label>Capture Screenshot</label>
                <Input
                  type="text"
                  value={formData.shortcuts.captureScreenshot}
                  onChange={(e) => updateNestedField('shortcuts', 'captureScreenshot', e.target.value)}
                />
              </FormGroup>
              <FormGroup>
                <label>Toggle Window</label>
                <Input
                  type="text"
                  value={formData.shortcuts.toggleWindow}
                  onChange={(e) => updateNestedField('shortcuts', 'toggleWindow', e.target.value)}
                />
              </FormGroup>
              <FormGroup>
                <label>Generate Solution</label>
                <Input
                  type="text"
                  value={formData.shortcuts.generateSolution}
                  onChange={(e) => updateNestedField('shortcuts', 'generateSolution', e.target.value)}
                />
              </FormGroup>
              <FormGroup>
                <label>Reset Problem</label>
                <Input
                  type="text"
                  value={formData.shortcuts.resetProblem}
                  onChange={(e) => updateNestedField('shortcuts', 'resetProblem', e.target.value)}
                />
              </FormGroup>
            </ShortcutGrid>
          </Section>

          <Section>
            <div className="title">🪟 Window Settings</div>
            <FormGroup>
              <CheckboxLabel>
                <Checkbox
                  type="checkbox"
                  checked={formData.windowSettings.alwaysOnTop}
                  onChange={(e) => updateNestedField('windowSettings', 'alwaysOnTop', e.target.checked)}
                />
                Always on top
              </CheckboxLabel>
            </FormGroup>
            <FormGroup>
              <CheckboxLabel>
                <Checkbox
                  type="checkbox"
                  checked={formData.windowSettings.startMinimized}
                  onChange={(e) => updateNestedField('windowSettings', 'startMinimized', e.target.checked)}
                />
                Start minimized
              </CheckboxLabel>
            </FormGroup>
          </Section>

          <Section>
            <div className="title">👁️ OCR Settings</div>
            <FormGroup>
              <label>Language</label>
              <Select
                value={formData.ocrSettings.language}
                onChange={(e) => updateNestedField('ocrSettings', 'language', e.target.value)}
              >
                <option value="eng">English</option>
                <option value="spa">Spanish</option>
                <option value="fra">French</option>
                <option value="deu">German</option>
                <option value="chi_sim">Chinese (Simplified)</option>
                <option value="jpn">Japanese</option>
              </Select>
            </FormGroup>
            <FormGroup>
              <label>Confidence Threshold (%)</label>
              <Input
                type="number"
                min="0"
                max="100"
                value={formData.ocrSettings.confidence}
                onChange={(e) => updateNestedField('ocrSettings', 'confidence', parseInt(e.target.value))}
              />
            </FormGroup>
          </Section>

          <ButtonGroup>
            <Button type="button" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              Save Settings
            </Button>
          </ButtonGroup>
        </form>
      </Modal>
    </Overlay>
  );
};

export default SettingsModal;
