"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
/**
 * Secure API bridge between main and renderer processes
 * This preload script exposes safe IPC methods to the renderer
 */
const electronAPI = {
    // Screenshot functionality
    captureScreenshot: () => electron_1.ipcRenderer.invoke('capture-screenshot'),
    // OCR functionality
    extractText: (imagePath) => electron_1.ipcRenderer.invoke('extract-text', imagePath),
    // AI functionality
    generateSolution: (problemText) => electron_1.ipcRenderer.invoke('generate-solution', problemText),
    // Settings management
    getSettings: () => electron_1.ipcRenderer.invoke('get-settings'),
    saveSettings: (settings) => electron_1.ipcRenderer.invoke('save-settings', settings),
    // Window management
    toggleWindow: () => electron_1.ipcRenderer.invoke('toggle-window'),
    minimizeWindow: () => electron_1.ipcRenderer.invoke('minimize-window'),
    closeWindow: () => electron_1.ipcRenderer.invoke('close-window'),
    quitApp: () => electron_1.ipcRenderer.invoke('quit-app'),
    // Data persistence
    saveProblem: (problem) => electron_1.ipcRenderer.invoke('save-problem', problem),
    getProblems: () => electron_1.ipcRenderer.invoke('get-problems'),
    saveSolution: (solution) => electron_1.ipcRenderer.invoke('save-solution', solution),
    getSolutions: (problemId) => electron_1.ipcRenderer.invoke('get-solutions', problemId),
    // Event listeners for main process events
    onScreenshotCaptured: (callback) => {
        electron_1.ipcRenderer.on('screenshot-captured', (_, imagePath) => callback(imagePath));
    },
    onTextExtracted: (callback) => {
        electron_1.ipcRenderer.on('text-extracted', (_, text) => callback(text));
    },
    onSolutionGenerated: (callback) => {
        electron_1.ipcRenderer.on('solution-generated', (_, solution) => callback(solution));
    }
};
// Expose the API to the renderer process
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
// Additional event listeners for global shortcuts
electron_1.ipcRenderer.on('generate-solution-shortcut', () => {
    window.dispatchEvent(new CustomEvent('generate-solution-shortcut'));
});
electron_1.ipcRenderer.on('reset-problem-shortcut', () => {
    window.dispatchEvent(new CustomEvent('reset-problem-shortcut'));
});
// Log that preload script has loaded
console.log('Code Xpo preload script loaded successfully');
//# sourceMappingURL=preload.js.map