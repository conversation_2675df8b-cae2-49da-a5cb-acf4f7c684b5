"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const fs_1 = require("fs");
const electron_store_1 = __importDefault(require("electron-store"));
const screenshot_desktop_1 = __importDefault(require("screenshot-desktop"));
const tesseract_js_1 = require("tesseract.js");
const generative_ai_1 = require("@google/generative-ai");
// Initialize electron store for persistent data
const store = new electron_store_1.default();
// Global variables
let mainWindow = null;
let isQuitting = false;
// Default settings
const defaultSettings = {
    geminiApiKey: '',
    shortcuts: {
        captureScreenshot: 'CommandOrControl+H',
        toggleWindow: 'CommandOrControl+Shift+X',
        generateSolution: 'CommandOrControl+Return',
        resetProblem: 'CommandOrControl+R',
        quitApp: 'CommandOrControl+Q'
    },
    windowSettings: {
        alwaysOnTop: false,
        startMinimized: false,
        width: 1200,
        height: 800
    },
    ocrSettings: {
        language: 'eng',
        confidence: 70
    }
};
/**
 * Create the main application window
 */
function createWindow() {
    const settings = getSettings();
    mainWindow = new electron_1.BrowserWindow({
        width: settings.windowSettings.width,
        height: settings.windowSettings.height,
        minWidth: 800,
        minHeight: 600,
        show: !settings.windowSettings.startMinimized,
        alwaysOnTop: settings.windowSettings.alwaysOnTop,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: (0, path_1.join)(__dirname, '../preload/preload.js'),
            webSecurity: true
        },
        titleBarStyle: 'default',
        icon: (0, path_1.join)(__dirname, '../../assets/icon.png')
    });
    // Load the app
    if (process.env.NODE_ENV === 'development') {
        mainWindow.loadURL('http://localhost:3000');
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile((0, path_1.join)(__dirname, '../renderer/index.html'));
    }
    // Handle window events
    mainWindow.on('close', (event) => {
        if (!isQuitting) {
            event.preventDefault();
            mainWindow?.hide();
        }
    });
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}
/**
 * Get application settings from store
 */
function getSettings() {
    return store.get('settings', defaultSettings);
}
/**
 * Save application settings to store
 */
function saveSettings(settings) {
    const currentSettings = getSettings();
    const newSettings = { ...currentSettings, ...settings };
    store.set('settings', newSettings);
}
/**
 * Register global keyboard shortcuts
 */
function registerGlobalShortcuts() {
    const settings = getSettings();
    // Clear existing shortcuts
    electron_1.globalShortcut.unregisterAll();
    // Capture screenshot shortcut
    electron_1.globalShortcut.register(settings.shortcuts.captureScreenshot, async () => {
        try {
            const imagePath = await captureScreenshot();
            mainWindow?.webContents.send('screenshot-captured', imagePath);
        }
        catch (error) {
            console.error('Screenshot capture failed:', error);
        }
    });
    // Toggle window shortcut
    electron_1.globalShortcut.register(settings.shortcuts.toggleWindow, () => {
        toggleWindow();
    });
    // Generate solution shortcut
    electron_1.globalShortcut.register(settings.shortcuts.generateSolution, () => {
        mainWindow?.webContents.send('generate-solution-shortcut');
    });
    // Reset problem shortcut
    electron_1.globalShortcut.register(settings.shortcuts.resetProblem, () => {
        mainWindow?.webContents.send('reset-problem-shortcut');
    });
    // Quit app shortcut
    electron_1.globalShortcut.register(settings.shortcuts.quitApp, () => {
        quitApp();
    });
}
/**
 * Capture screenshot of the entire screen
 */
async function captureScreenshot() {
    try {
        const displays = electron_1.screen.getAllDisplays();
        const primaryDisplay = displays.find(display => display.bounds.x === 0 && display.bounds.y === 0) || displays[0];
        const img = await (0, screenshot_desktop_1.default)({ screen: primaryDisplay.id });
        const timestamp = Date.now();
        const filename = `screenshot_${timestamp}.png`;
        const filepath = (0, path_1.join)(electron_1.app.getPath('userData'), 'screenshots', filename);
        // Ensure screenshots directory exists
        const screenshotsDir = (0, path_1.join)(electron_1.app.getPath('userData'), 'screenshots');
        if (!(0, fs_1.existsSync)(screenshotsDir)) {
            require('fs').mkdirSync(screenshotsDir, { recursive: true });
        }
        // Save screenshot
        require('fs').writeFileSync(filepath, img);
        return filepath;
    }
    catch (error) {
        console.error('Screenshot capture error:', error);
        throw new Error('Failed to capture screenshot');
    }
}
/**
 * Extract text from image using OCR
 */
async function extractTextFromImage(imagePath) {
    try {
        const worker = await (0, tesseract_js_1.createWorker)('eng');
        const { data: { text } } = await worker.recognize(imagePath);
        await worker.terminate();
        return text.trim();
    }
    catch (error) {
        console.error('OCR extraction error:', error);
        throw new Error('Failed to extract text from image');
    }
}
/**
 * Generate AI solution using Gemini API
 */
async function generateAISolution(problemText) {
    try {
        const settings = getSettings();
        if (!settings.geminiApiKey) {
            throw new Error('Gemini API key not configured');
        }
        const genAI = new generative_ai_1.GoogleGenerativeAI(settings.geminiApiKey);
        const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
        const prompt = `
You are an expert coding interview assistant. Given the following coding problem, provide:
1. A clean, optimized solution in Python
2. Detailed explanation of the approach
3. Time complexity analysis
4. Space complexity analysis

Problem:
${problemText}

Please format your response as JSON with the following structure:
{
  "code": "your solution code here",
  "explanation": "detailed explanation",
  "timeComplexity": "O(n) format",
  "spaceComplexity": "O(n) format"
}
`;
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const text = response.text();
        // Try to parse JSON response
        let parsedResponse;
        try {
            // Extract JSON from response if it's wrapped in markdown
            const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```/) || text.match(/\{[\s\S]*\}/);
            const jsonText = jsonMatch ? jsonMatch[1] || jsonMatch[0] : text;
            parsedResponse = JSON.parse(jsonText);
        }
        catch (parseError) {
            // Fallback if JSON parsing fails
            parsedResponse = {
                code: text,
                explanation: 'AI generated solution',
                timeComplexity: 'Analysis not available',
                spaceComplexity: 'Analysis not available'
            };
        }
        const solution = {
            id: `solution_${Date.now()}`,
            problemId: `problem_${Date.now()}`,
            code: parsedResponse.code || text,
            language: 'python',
            explanation: parsedResponse.explanation || 'No explanation provided',
            timeComplexity: parsedResponse.timeComplexity || 'Not analyzed',
            spaceComplexity: parsedResponse.spaceComplexity || 'Not analyzed',
            timestamp: Date.now()
        };
        return solution;
    }
    catch (error) {
        console.error('AI solution generation error:', error);
        throw new Error('Failed to generate AI solution');
    }
}
/**
 * Toggle window visibility
 */
function toggleWindow() {
    if (!mainWindow)
        return;
    if (mainWindow.isVisible()) {
        mainWindow.hide();
    }
    else {
        mainWindow.show();
        mainWindow.focus();
    }
}
/**
 * Quit the application
 */
function quitApp() {
    isQuitting = true;
    electron_1.globalShortcut.unregisterAll();
    electron_1.app.quit();
}
// App event handlers
electron_1.app.whenReady().then(() => {
    createWindow();
    registerGlobalShortcuts();
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        quitApp();
    }
});
electron_1.app.on('before-quit', () => {
    isQuitting = true;
});
// IPC handlers
electron_1.ipcMain.handle('capture-screenshot', async () => {
    return await captureScreenshot();
});
electron_1.ipcMain.handle('extract-text', async (_, imagePath) => {
    return await extractTextFromImage(imagePath);
});
electron_1.ipcMain.handle('generate-solution', async (_, problemText) => {
    return await generateAISolution(problemText);
});
electron_1.ipcMain.handle('get-settings', () => {
    return getSettings();
});
electron_1.ipcMain.handle('save-settings', (_, settings) => {
    saveSettings(settings);
    registerGlobalShortcuts(); // Re-register shortcuts if they changed
});
electron_1.ipcMain.handle('toggle-window', () => {
    toggleWindow();
});
electron_1.ipcMain.handle('minimize-window', () => {
    mainWindow?.minimize();
});
electron_1.ipcMain.handle('close-window', () => {
    mainWindow?.hide();
});
electron_1.ipcMain.handle('quit-app', () => {
    quitApp();
});
electron_1.ipcMain.handle('save-problem', (_, problem) => {
    const problems = store.get('problems', []);
    problems.push(problem);
    store.set('problems', problems);
});
electron_1.ipcMain.handle('get-problems', () => {
    return store.get('problems', []);
});
electron_1.ipcMain.handle('save-solution', (_, solution) => {
    const solutions = store.get('solutions', []);
    solutions.push(solution);
    store.set('solutions', solutions);
});
electron_1.ipcMain.handle('get-solutions', (_, problemId) => {
    const solutions = store.get('solutions', []);
    return solutions.filter(solution => solution.problemId === problemId);
});
//# sourceMappingURL=main.js.map