{"name": "code-xpo", "version": "1.0.0", "description": "AI-powered coding interview assistant with screenshot capture and OCR", "main": "dist/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite", "dev:main": "tsc -p tsconfig.main.json && electron dist/main/main.js --dev", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "start": "electron dist/main/main.js", "pack": "electron-builder", "dist": "npm run build && electron-builder", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electron", "react", "typescript", "coding-interview", "ai", "ocr", "screenshot"], "author": "Code Xpo Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.3.3", "vite": "^5.0.8", "vite-plugin-electron": "^0.15.5"}, "dependencies": {"@google/generative-ai": "^0.2.1", "electron-store": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tesseract.js": "^5.0.4", "screenshot-desktop": "^1.15.0", "electron-localshortcut": "^3.2.1", "styled-components": "^6.1.6", "@types/styled-components": "^5.1.34"}, "build": {"appId": "com.codexpo.app", "productName": "Code Xpo", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}