import React, { useEffect, useState } from 'react';
import styled, { keyframes } from 'styled-components';

const slideIn = keyframes`
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const slideOut = keyframes`
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
`;

const Container = styled.div<{ type: 'success' | 'error' | 'info'; isExiting: boolean }>`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 3000;
  min-width: 300px;
  max-width: 400px;
  padding: 16px 20px;
  border-radius: 12px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  animation: ${props => props.isExiting ? slideOut : slideIn} 0.3s ease-out;
  
  ${props => {
    switch (props.type) {
      case 'success':
        return `
          background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(34, 197, 94, 0.1));
          border-color: rgba(34, 197, 94, 0.3);
        `;
      case 'error':
        return `
          background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
          border-color: rgba(239, 68, 68, 0.3);
        `;
      case 'info':
        return `
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
          border-color: rgba(59, 130, 246, 0.3);
        `;
    }
  }}
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }
`;

const Icon = styled.div<{ type: 'success' | 'error' | 'info' }>`
  font-size: 18px;
  flex-shrink: 0;
  
  ${props => {
    switch (props.type) {
      case 'success':
        return 'color: #22c55e;';
      case 'error':
        return 'color: #ef4444;';
      case 'info':
        return 'color: #3b82f6;';
    }
  }}
`;

const Content = styled.div`
  flex: 1;
  line-height: 1.4;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  flex-shrink: 0;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
`;

const ProgressBar = styled.div<{ duration: number }>`
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 0 12px 12px;
  animation: progress ${props => props.duration}ms linear;
  
  @keyframes progress {
    from {
      width: 100%;
    }
    to {
      width: 0%;
    }
  }
`;

interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'info';
  onClose: () => void;
  duration?: number;
}

const Toast: React.FC<ToastProps> = ({ 
  message, 
  type, 
  onClose, 
  duration = 5000 
}) => {
  const [isExiting, setIsExiting] = useState(false);

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'info':
        return 'ℹ️';
    }
  };

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(onClose, 300); // Wait for animation to complete
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration]);

  return (
    <Container 
      type={type} 
      isExiting={isExiting}
      onClick={handleClose}
    >
      <Icon type={type}>{getIcon()}</Icon>
      <Content>{message}</Content>
      <CloseButton onClick={(e) => {
        e.stopPropagation();
        handleClose();
      }}>
        ×
      </CloseButton>
      <ProgressBar duration={duration} />
    </Container>
  );
};

export default Toast;
