import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron'
import { ElectronAPI, AppSettings, CodingProblem, AISolution } from '../types'

/**
 * Secure API bridge between main and renderer processes
 * This preload script exposes safe IPC methods to the renderer
 */
const electronAPI: ElectronAPI = {
  // Screenshot functionality
  captureScreenshot: () => ipcRenderer.invoke('capture-screenshot'),

  // OCR functionality
  extractText: (imagePath: string) =>
    ipcRenderer.invoke('extract-text', imagePath),

  // AI functionality
  generateSolution: (problemText: string) =>
    ipcRenderer.invoke('generate-solution', problemText),

  // Settings management
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings: Partial<AppSettings>) =>
    ipcRenderer.invoke('save-settings', settings),

  // Window management
  toggleWindow: () => ipcRenderer.invoke('toggle-window'),
  minimizeWindow: () => ipc<PERSON>enderer.invoke('minimize-window'),
  closeWindow: () => ipc<PERSON>enderer.invoke('close-window'),
  quitApp: () => ipcRenderer.invoke('quit-app'),

  // Data persistence
  saveProblem: (problem: CodingProblem) =>
    ipcRenderer.invoke('save-problem', problem),
  getProblems: () => ipcRenderer.invoke('get-problems'),
  saveSolution: (solution: AISolution) =>
    ipcRenderer.invoke('save-solution', solution),
  getSolutions: (problemId: string) =>
    ipcRenderer.invoke('get-solutions', problemId),

  // Event listeners for main process events
  onScreenshotCaptured: (callback: (imagePath: string) => void) => {
    ipcRenderer.on('screenshot-captured', (_, imagePath) => callback(imagePath))
  },

  onTextExtracted: (callback: (text: string) => void) => {
    ipcRenderer.on('text-extracted', (_, text) => callback(text))
  },

  onSolutionGenerated: (callback: (solution: AISolution) => void) => {
    ipcRenderer.on('solution-generated', (_, solution) => callback(solution))
  }
}

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// Additional event listeners for global shortcuts
ipcRenderer.on('generate-solution-shortcut', () => {
  ;(window as any).dispatchEvent(
    new (window as any).CustomEvent('generate-solution-shortcut')
  )
})

ipcRenderer.on('reset-problem-shortcut', () => {
  ;(window as any).dispatchEvent(
    new (window as any).CustomEvent('reset-problem-shortcut')
  )
})

// Log that preload script has loaded
console.log('Code Xpo preload script loaded successfully')
